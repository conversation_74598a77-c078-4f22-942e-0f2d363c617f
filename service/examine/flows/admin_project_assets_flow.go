package flows

import (
	"transwarp.io/applied-ai/central-auth-service/models"
	"transwarp.io/applied-ai/central-auth-service/service/examine/flows/callback"
)

const (
	AdminProjectAssetsFlowName = "admin_project_assets_flow"
)

var AdminProjectAssetsFlow = &models.ExamineFlow{
	Name: AdminProjectAssetsFlowName,
	Abbr: "AP",
	Nodes: []*models.ExamineNode{
		{
			Index:     1,
			PrevIndex: 0,
			NextIndex: 2,
		},
		{
			Index:     2,
			PrevIndex: 1,
			NextIndex: 0,
			RoleRequest: []*models.RoleRequest{
				{
					Type:     models.RoleRequestAssets,
					RoleName: "空间负责人",
					Name:     "公共空间负责人（或签）",
				},
			},
		},
	},
	Callbacks: []models.FlowFunctionCall{
		{
			Status:   models.Finished,
			Callback: callback.HttpCallback,
		},
	},
}
