package flows

import (
	"transwarp.io/applied-ai/central-auth-service/models"
	"transwarp.io/applied-ai/central-auth-service/service/examine/flows/callback"
)

const (
	ProjectAssetsFlowName = "project_assets_flow"
)

var ProjectAssetsFlow = &models.ExamineFlow{
	Name: ProjectAssetsFlowName,
	Abbr: "PA",
	Nodes: []*models.ExamineNode{
		{
			Index:     1,
			PrevIndex: 0,
			NextIndex: 2,
		},
		{
			Index:     2,
			PrevIndex: 1,
			NextIndex: 3,
			RoleRequest: []*models.RoleRequest{
				{
					Type:     models.RoleRequestProject,
					RoleName: "空间负责人",
					Name:     "所属空间负责人（或签）",
				},
			},
		},
		{
			Index:     3,
			PrevIndex: 2,
			NextIndex: 0,
			RoleRequest: []*models.RoleRequest{
				{
					Type:     models.RoleRequestAssets,
					RoleName: "空间负责人",
					Name:     "公共空间负责人（或签）",
				},
			},
		},
	},
	Callbacks: []models.FlowFunctionCall{
		{
			Status:   models.Finished,
			Callback: callback.HttpCallback,
		},
	},
}
