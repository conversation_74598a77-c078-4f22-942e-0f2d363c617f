package flows

import (
	"transwarp.io/applied-ai/central-auth-service/models"
	"transwarp.io/applied-ai/central-auth-service/service/examine/flows/callback"
)

const (
	ServingDeployFlowName = "serving_deploy_flow"
)

var ServingDeployFlow = &models.ExamineFlow{
	Name: ServingDeployFlowName,
	Abbr: "SD",
	Nodes: []*models.ExamineNode{
		{
			Index:     1,
			PrevIndex: 0,
			NextIndex: 2,
			Callbacks: []models.NodeFunctionCall{
				{
					Result:   models.Approve,
					Callback: callback.ServingUnderApprovalHttpCall,
				},
			},
		},
		{
			Index:     2,
			PrevIndex: 1,
			NextIndex: 0,
			RoleRequest: []*models.RoleRequest{
				{
					Type:     models.RoleRequestProject,
					RoleName: "空间负责人",
					Name:     "所属空间负责人（或签）",
				},
			},
			Callbacks: []models.NodeFunctionCall{
				{
					Result:   models.Approve,
					Callback: callback.ServingApprovalPassedHttpCall,
				},
				{
					Result:   models.Reject,
					Callback: callback.ServingApprovalRejectHttpCall,
				},
			},
		},
	},
	Callbacks: []models.FlowFunctionCall{
		{
			Status:   models.Finished,
			Callback: callback.HttpCallback,
		},
	},
}
