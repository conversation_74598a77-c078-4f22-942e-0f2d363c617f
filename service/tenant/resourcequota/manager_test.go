package resourcequota

import (
	"fmt"
	"strconv"
	"testing"
	"time"

	"k8s.io/apimachinery/pkg/api/resource"
	"transwarp.io/aip/llmops-common/pkg/crd"
	"transwarp.io/applied-ai/central-auth-service/service/tenant/util"
)

// TestFlushDefaultResourceQuotaV2ToYaml 测试将默认资源配额写入YAML文件
func TestFlushDefaultResourceQuotaV2ToYaml(t *testing.T) {
	crd.Init()

	manager, err := GetResourceQuotaManager()
	if err != nil {
		t.Fatalf("Failed to get resource quota manager: %v", err)
	}

	defaultQuota := manager.GetDefaultQuota()
	if defaultQuota == nil {
		t.Fatal("Failed to get default resource quota")
	}

	// err = manager.CreateOrUpgradeResourceQuota("dev-ls", &defaultQuota.Hard)
	// if err != nil {
	// 	t.Fatalf("Failed to create or upgrade resource quota: %v", err)
	// }
	spec1 := manager.getDefaultLimitsResourceQuotaSpec()
	if spec1 == nil {
		t.Fatal("Failed to get default limits resource quota spec")
	}
	limits, _ := manager.GetClusterResourceLimits()
	if limits == nil {
		t.Fatal("Failed to get cluster resource limits")
	}

	manager.renderValues("llmops-zzz", limits)

	spec, err := manager.getResourcequota("dev-ls")
	if err != nil {
		t.Fatalf("Failed to get resource quota: %v", err)
	}
	if spec == nil {
		t.Fatal("Failed to get resource quota")
	}
	time.Sleep(10 * time.Minute)
}

func TestNxgToResourceQuotaSpec(t *testing.T) {
	crd.Init()

	manager, err := GetResourceQuotaManager()
	if err != nil {
		t.Fatalf("Failed to get resource quota manager: %v", err)
	}

	spec, err := manager.NxgToResourceQuotaSpec([]string{"hyx-test"})
	if err != nil {
		t.Fatalf("Failed to convert nxg to resource quota: %v", err)
	}
	if spec == nil {
		t.Fatal("Failed to convert nxg to resource quota")
	}
}

func TestResourceQuantity(t *testing.T) {
	unit := resource.MustParse("16Mi")
	totalMem := resource.MustParse("256")
	unit.Mul(totalMem.Value())
	fmt.Printf("  unit.String(): %s\n", unit.String())
	fmt.Printf("  totalMem.String(): %s\n", totalMem.String())
	fmt.Printf("  totalMem.Value():  %d\n", totalMem.Value())

	finalValue := totalMem.Value()
	formattedResult := resource.NewQuantity(finalValue, resource.BinarySI)

	fmt.Printf("  Formatted result (NewQuantity): %s\n", formattedResult.String())
	fmt.Printf("  totalMem.Value():  %d\n", totalMem.Value())

	originalVal := formattedResult.Value() / unit.Value()
	fmt.Printf("  originVal: %s\n", strconv.FormatInt(originalVal, 10))
}

func TestCalculateByWeight(t *testing.T) {
	quantityStr := "100Gi"
	weight := 0.5
	result, err := util.CalculateByWeight(quantityStr, weight)
	if err != nil {
		t.Fatalf("Failed to calculate by weight: %v", err)
	}
	fmt.Printf("  result: %s\n", result)

	quantityStr = "8Gi"
	weight = 0.15
	result, err = util.CalculateByWeight(quantityStr, weight)
	if err != nil {
		t.Fatalf("Failed to calculate by weight: %v", err)
	}
	fmt.Printf("  result: %s\n", result)

	quantityStr = "8"
	weight = 0.3
	result, err = util.CalculateByWeight(quantityStr, weight)
	if err != nil {
		t.Fatalf("Failed to calculate by weight: %v", err)
	}
	fmt.Printf("  result: %s\n", result)
}

func TestDefaultQuota(t *testing.T) {
	crd.Init()

	manager := MustGetManager()
	if manager == nil {
		t.Fatal("Failed to get default resource quota")
	}

	quota := manager.GetDefaultQuota()
	fmt.Printf("  quota: %+v\n", quota)
}
