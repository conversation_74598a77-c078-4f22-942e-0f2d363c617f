package resourcequota

import (
	"context"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/models"
	helm "transwarp.io/applied-ai/central-auth-service/service/tenant/helm"
)

type QuotaServiceImpl struct {
	quotaRepo      QuotaRepo
	configRepo     ConfigRepo
	calculator     *QuotaCalculator
	validator      *QuotaValidator
	renderer       *HelmValuesRenderer
	nxpuClient     *NXPUClient
	configProvider *DefaultConfigProvider
}

func NewQuotaService(
	quotaRepo QuotaRepo,
	configRepo ConfigRepo,
	calculator *QuotaCalculator,
	validator *QuotaValidator,
	renderer *HelmValuesRenderer,
	nxpuClient *NXPUClient,
) QuotaService {
	return &QuotaServiceImpl{
		quotaRepo:      quotaRepo,
		configRepo:     configRepo,
		calculator:     calculator,
		validator:      validator,
		renderer:       renderer,
		nxpuClient:     nxpuClient,
		configProvider: NewDefaultConfigProvider(),
	}
}

func (s *QuotaServiceImpl) GetQuota(ctx context.Context, namespace string) (*models.TenantResourceQuota, error) {
	if err := s.validator.ValidateNamespace(namespace); err != nil {
		return nil, stderr.Errorf("invalid namespace: %v", err)
	}
	spec, err := s.quotaRepo.GetQuota(ctx, namespace)
	if err != nil {
		return nil, err
	}

	quota := &models.TenantResourceQuota{
		Hard: *spec,
	}

	return quota, nil
}

func (s *QuotaServiceImpl) CreateOrUpdateQuota(ctx context.Context, namespace string, spec *models.ResourceQuotaSpec) error {
	if err := s.validator.ValidateNamespace(namespace); err != nil {
		return stderr.Errorf("invalid namespace: %v", err)
	}

	if err := s.validator.ValidateSpec(spec); err != nil {
		return stderr.Errorf("invalid quota spec: %v", err)
	}

	limits, err := s.GetClusterLimits()
	if err != nil {
		stdlog.Warnf("Failed to get cluster limits: %v", err)
	} else {
		if err := s.validator.ValidateQuotaLimits(spec, limits); err != nil {
			return stderr.Errorf("quota exceeds limits: %v", err)
		}
	}

	if err := s.quotaRepo.SaveQuota(ctx, namespace, spec); err != nil {
		return stderr.Errorf("failed to save quota: %v", err)
	}

	stdlog.Infof("Successfully created/updated quota for namespace %s", namespace)
	return nil
}

func (s *QuotaServiceImpl) GetDefaultQuota() *models.TenantResourceQuota {
	defaultQuota := s.configProvider.GetDefaultQuota()

	limits, err := s.GetClusterLimits()
	if err != nil {
		stdlog.Errorf("Failed to get cluster limits: %v", err)
		defaultQuota.Limits = *s.configProvider.GetDefaultLimitsSpec()
	} else {
		defaultQuota.Limits = *limits
	}

	defaultQuota.Hard.XPUTypeCount = len(defaultQuota.Hard.AcceleratedComputing)

	return defaultQuota
}

func (s *QuotaServiceImpl) GetClusterLimits() (*models.ResourceQuotaSpec, error) {
	limits := s.configProvider.GetDefaultLimitsSpec()

	totalCpu, totalMemory, err := s.calculator.CalculateClusterTotals()
	if err != nil {
		return nil, stderr.Errorf("failed to calculate cluster totals: %v", err)
	}

	limits.LimitsCpu = totalCpu
	limits.LimitsMemory = totalMemory
	limits.CPU.NominalQuota = totalCpu
	limits.Memory.NominalQuota = totalMemory

	// TODO cal XPU limits

	return limits, nil
}

func (s *QuotaServiceImpl) ValidateQuota(spec *models.ResourceQuotaSpec) error {
	return s.validator.ValidateSpec(spec)
}

func (s *QuotaServiceImpl) CalculateQuotaFromXPUGroups(groupIDs []string) (*models.ResourceQuotaSpec, error) {
	if len(groupIDs) == 0 {
		return nil, stderr.Errorf("no XPU groups provided")
	}

	spec, err := s.nxpuClient.NXPUGroupToResourceQuotaSpec(groupIDs)
	if err != nil {
		return nil, err
	}
	spec.QuotaType = models.QuotaTypeDynamic

	return spec, nil
}

func (s *QuotaServiceImpl) deployQuotaConfig(ctx context.Context, namespace string, spec *models.ResourceQuotaSpec) error {
	s.calculator.RecalculateQuota(spec)

	valuesFile, err := s.renderer.RenderValues(namespace, spec)
	if err != nil {
		return stderr.Errorf("failed to render values: %v", err)
	}

	if err := helm.InstallLlmopsQueue(namespace, []string{valuesFile}); err != nil {
		return stderr.Errorf("failed to deploy helm release: %v", err)
	}

	return nil
}

func (s *QuotaServiceImpl) StartConfigWatcher(ctx context.Context) error {
	configChan, err := s.configRepo.WatchConfig(ctx)
	if err != nil {
		return stderr.Errorf("failed to start config watcher: %v", err)
	}

	go func() {
		for {
			select {
			case event := <-configChan:
				s.handleConfigChange(ctx, event)
			case <-ctx.Done():
				return
			}
		}
	}()

	return nil
}

func (s *QuotaServiceImpl) StartXPUWatcher(ctx context.Context) error {
	xpuChan, err := s.nxpuClient.WatchXPUGroups(ctx)
	if err != nil {
		return stderr.Errorf("failed to start XPUGroup watcher: %v", err)
	}

	go func() {
		for {
			select {
			case event := <-xpuChan:
				s.handleXPUGroupChange(ctx, event)
			case <-ctx.Done():
				return
			}
		}
	}()

	return nil
}

func (s *QuotaServiceImpl) handleConfigChange(ctx context.Context, event ConfigChangeEvent) {
	stdlog.Infof("Config change detected for namespace %s: %s", event.Namespace, event.EventType)

	switch event.EventType {
	case ConfigEventTypeCreated, ConfigEventTypeUpdated:
		if err := s.redeployConfig(ctx, event.Namespace); err != nil {
			stdlog.Errorf("Failed to redeploy config for namespace %s: %v", event.Namespace, err)
		}
	case ConfigEventTypeDeleted:
		if err := helm.UninstallLlmopsQueue(event.Namespace); err != nil {
			stdlog.Errorf("Failed to uninstall helm release for namespace %s: %v", event.Namespace, err)
		}
	}
}

func (s *QuotaServiceImpl) handleXPUGroupChange(ctx context.Context, event XPUGroupChangeEvent) {
	stdlog.Infof("XPU group change detected: %s %s", event.GroupID, event.EventType)

	newSpecs, err := s.nxpuClient.RecalculateNamespaceResourceQuotaEffectByNXG(event.GroupID)
	if err != nil {
		stdlog.Errorf("Failed to recalculate namespace resource quota effect by nxg: %v", err)
	}
	for ns, newSpec := range newSpecs {
		oldSepc, err := s.quotaRepo.GetQuota(ctx, ns)
		if err != nil {
			stdlog.Errorf("Failed to get quota for namespace %s: %v", ns, err)
			continue
		}
		if oldSepc.QuotaType != models.QuotaTypeDynamic {
			continue
		}

		oldSepc.ApplyNewQuota(&newSpec)
		if s.CreateOrUpdateQuota(ctx, ns, oldSepc) != nil {
			stdlog.Errorf("Failed to save quota for namespace %s: %v", ns, err)
			continue
		}
	}
}

func (s *QuotaServiceImpl) redeployConfig(ctx context.Context, namespace string) error {
	spec, err := s.quotaRepo.GetQuota(ctx, namespace)
	if err != nil {
		return err
	}

	return s.deployQuotaConfig(ctx, namespace, spec)
}
