package resourcequota

import (
	"fmt"
	"math"
	"regexp"
	"strconv"

	"k8s.io/apimachinery/pkg/api/resource"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
)

func ValidateNamespaceName(namespace string) bool {
	namespaceRegex := regexp.MustCompile(`^[a-z0-9]([-a-z0-9]*[a-z0-9])?$`)

	if len(namespace) < 1 || len(namespace) > MaxNamespaceLength {
		return false
	}

	return namespaceRegex.MatchString(namespace)
}

func TruncateString(str string, maxLength int) string {
	if len(str) > maxLength {
		return str[:maxLength]
	}
	return str
}

func GetResourceQuotaName(namespace string) string {
	return TruncateString(fmt.Sprintf("%s-default", namespace), MaxNamespaceLength)
}

// BytesToGiOrMi 将字节转换为Gi或Mi
func BytesToGiOrMi(bytes int64) string {
	gi := float64(bytes) / (1 << 30)
	mi := float64(bytes) / (1 << 20)

	if gi == float64(int64(gi)) {
		return fmt.Sprintf("%dGi", int64(gi))
	} else {
		return fmt.Sprintf("%dMi", int64(mi))
	}
}

// BytesToMiWithoutUnit 将字节转换为Mi（不带单位）
func BytesToMiWithoutUnit(bytes int64) int64 {
	return bytes / (1 << 20)
}

// GiToGi 将Gi转换为Gi字符串
func GiToGi(gi int64) string {
	return fmt.Sprintf("%dGi", gi)
}

// ToGiFloat 将字节转换为Gi（浮点数）
func ToGiFloat(v int64) string {
	gi := float64(v) / (1 << 30)
	return strconv.FormatFloat(gi, 'f', 2, 64) + "Gi"
}

// ToGiInt 将字节转换为Gi（整数）
func ToGiInt(v int64) string {
	gi := v / (1 << 30)
	return strconv.FormatInt(gi, 10) + "Gi"
}

// ZeroWithUnit 返回带单位的零值
func ZeroWithUnit(x string) string {
	if x == "" {
		return "0"
	}

	re := regexp.MustCompile(`^(\d+)(.*)$`)
	matches := re.FindStringSubmatch(x)

	if len(matches) > 1 {
		if len(matches) > 2 && matches[2] != "" {
			return "0" + matches[2]
		}
		return "0"
	}

	return x
}

// ToGiStrCeil 将资源量转换为Gi字符串（向上取整）
func ToGiStrCeil(q resource.Quantity) string {
	bytes := q.Value() // int64 bytes
	gi := float64(bytes) / float64(1<<30)
	ceilGi := int64(math.Ceil(gi))
	return fmt.Sprintf("%dGi", ceilGi)
}

// RemoveDuplicatesString 去除字符串切片中的重复项
func RemoveDuplicatesString(slice []string) []string {
	seen := make(map[string]struct{})
	uniqueSlice := []string{}

	for _, elem := range slice {
		if _, exists := seen[elem]; !exists {
			seen[elem] = struct{}{}
			uniqueSlice = append(uniqueSlice, elem)
		}
	}
	return uniqueSlice
}

// Contains 检查字符串切片是否包含指定项
func Contains(slice []string, item string) bool {
	for _, elem := range slice {
		if elem == item {
			return true
		}
	}
	return false
}

// ParseQuantityString 解析数量字符串
func ParseQuantityString(str string) (value int64, unit string, err error) {
	re := regexp.MustCompile(`^(\d+)(.*)$`)
	matches := re.FindStringSubmatch(str)

	if len(matches) < 2 {
		return 0, "", stderr.Errorf("invalid quantity format: %s", str)
	}

	value, err = strconv.ParseInt(matches[1], 10, 64)
	if err != nil {
		return 0, "", stderr.Errorf("failed to parse value: %v", err)
	}

	if len(matches) > 2 {
		unit = matches[2]
	}

	return value, unit, nil
}

// FormatQuantity 格式化数量
func FormatQuantity(value int64, unit string) string {
	if unit == "" {
		return strconv.FormatInt(value, 10)
	}
	return fmt.Sprintf("%d%s", value, unit)
}

// IsZeroQuantity 检查是否为零值
func IsZeroQuantity(str string) bool {
	value, _, err := ParseQuantityString(str)
	if err != nil {
		return false
	}
	return value == 0
}

// CompareQuantities 比较两个数量字符串
func CompareQuantities(a, b string) (int, error) {
	qA, err := resource.ParseQuantity(a)
	if err != nil {
		return 0, stderr.Errorf("failed to parse quantity %s: %v", a, err)
	}

	qB, err := resource.ParseQuantity(b)
	if err != nil {
		return 0, stderr.Errorf("failed to parse quantity %s: %v", b, err)
	}

	return qA.Cmp(qB), nil
}

// AddQuantities 相加两个数量字符串
func AddQuantities(a, b string) (string, error) {
	qA, err := resource.ParseQuantity(a)
	if err != nil {
		return "", stderr.Errorf("failed to parse quantity %s: %v", a, err)
	}

	qB, err := resource.ParseQuantity(b)
	if err != nil {
		return "", stderr.Errorf("failed to parse quantity %s: %v", b, err)
	}

	qA.Add(qB)
	return qA.String(), nil
}

// SubtractQuantities 相减两个数量字符串
func SubtractQuantities(a, b string) (string, error) {
	qA, err := resource.ParseQuantity(a)
	if err != nil {
		return "", stderr.Errorf("failed to parse quantity %s: %v", a, err)
	}

	qB, err := resource.ParseQuantity(b)
	if err != nil {
		return "", stderr.Errorf("failed to parse quantity %s: %v", b, err)
	}

	qA.Sub(qB)
	return qA.String(), nil
}

// GenerateConfigMapName 生成ConfigMap名称
func GenerateConfigMapName(namespace string) string {
	return fmt.Sprintf("%s-resource-quota", namespace)
}

func GenerateConfigMapLabels() map[string]string {
	return map[string]string{
		ConfigMapLabelManagedBy: ConfigMapManagedByValue,
		ConfigMapLabelComponent: ConfigMapComponentValue,
	}
}
