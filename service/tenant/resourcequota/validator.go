package resourcequota

import (
	"strings"

	"k8s.io/apimachinery/pkg/api/resource"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/central-auth-service/models"
)

type QuotaValidator struct{}

func NewQuotaValidator() *QuotaValidator {
	return &QuotaValidator{}
}

func (v *QuotaValidator) ValidateSpec(spec *models.ResourceQuotaSpec) error {
	if spec == nil {
		return stderr.Errorf(ErrMsgInvalidQuotaSpec)
	}

	if err := v.validateBasicResources(spec); err != nil {
		return err
	}

	if err := v.ValidateQuotaItem(&spec.CPU); err != nil {
		return stderr.Errorf("invalid CPU quota: %v", err)
	}

	if err := v.ValidateQuotaItem(&spec.Memory); err != nil {
		return stderr.Errorf("invalid Memory quota: %v", err)
	}

	if err := v.validateAcceleratedComputing(spec.AcceleratedComputing); err != nil {
		return err
	}

	if spec.Queue != nil {
		if err := v.validateQueueQuota(spec.Queue); err != nil {
			return err
		}
	}

	if err := v.validateWeightMode(spec); err != nil {
		return err
	}

	return nil
}

func (v *QuotaValidator) ValidateQuotaItem(item *models.ResourceQuotaItem) error {
	if item == nil {
		return stderr.Errorf(ErrMsgInvalidQuotaItem)
	}

	if item.Name == "" {
		return stderr.Errorf("quota item name cannot be empty")
	}

	if err := v.validateQuantity(item.NominalQuota); err != nil {
		return stderr.Errorf("invalid nominal quota: %v", err)
	}

	if item.BorrowingLimit != "" {
		if err := v.validateQuantity(item.BorrowingLimit); err != nil {
			return stderr.Errorf("invalid borrowing limit: %v", err)
		}
	}

	if item.LendingLimit != "" {
		if err := v.validateQuantity(item.LendingLimit); err != nil {
			return stderr.Errorf("invalid lending limit: %v", err)
		}
	}

	if err := v.validateQuotaItemType(item.QuotaItemType); err != nil {
		return err
	}

	if item.Weight < 0 || item.Weight > 1 {
		return stderr.Errorf("weight must be between 0 and 1")
	}

	return nil
}

func (v *QuotaValidator) ValidateNamespace(namespace string) error {
	if namespace == "" {
		return stderr.Errorf("namespace cannot be empty")
	}

	if !ValidateNamespaceName(namespace) {
		return stderr.Errorf(ErrMsgInvalidNamespace)
	}

	return nil
}

func (v *QuotaValidator) ValidateQuotaLimits(spec *models.ResourceQuotaSpec, limits *models.ResourceQuotaSpec) error {
	if spec == nil || limits == nil {
		return stderr.Errorf("spec and limits cannot be nil")
	}

	if err := v.compareQuantities(spec.CPU.NominalQuota, limits.CPU.NominalQuota, "CPU"); err != nil {
		return err
	}

	if err := v.compareQuantities(spec.Memory.NominalQuota, limits.Memory.NominalQuota, "Memory"); err != nil {
		return err
	}

	for xpuType, items := range spec.AcceleratedComputing {
		limitItems, exists := limits.AcceleratedComputing[xpuType]
		if !exists {
			continue
		}

		for _, item := range items {
			for _, limitItem := range limitItems {
				if item.Name == limitItem.Name {
					if err := v.compareQuantities(item.NominalQuota, limitItem.NominalQuota, string(item.Name)); err != nil {
						return err
					}
					break
				}
			}
		}
	}

	return nil
}

func (v *QuotaValidator) validateBasicResources(spec *models.ResourceQuotaSpec) error {
	if spec.Pods != "" {
		if err := v.validateQuantity(spec.Pods); err != nil {
			return stderr.Errorf("invalid pods quota: %v", err)
		}
	}

	if spec.LimitsCpu != "" {
		if err := v.validateQuantity(spec.LimitsCpu); err != nil {
			return stderr.Errorf("invalid limits CPU: %v", err)
		}
	}

	if spec.LimitsMemory != "" {
		if err := v.validateQuantity(spec.LimitsMemory); err != nil {
			return stderr.Errorf("invalid limits memory: %v", err)
		}
	}

	if spec.RequestsStorage != "" {
		if err := v.validateQuantity(spec.RequestsStorage); err != nil {
			return stderr.Errorf("invalid requests storage: %v", err)
		}
	}

	if spec.FileStorage != "" {
		if err := v.validateQuantity(spec.FileStorage); err != nil {
			return stderr.Errorf("invalid file storage: %v", err)
		}
	}

	if spec.Bandwidth != "" {
		if err := v.validateQuantity(spec.Bandwidth); err != nil {
			return stderr.Errorf("invalid bandwidth: %v", err)
		}
	}

	return nil
}

func (v *QuotaValidator) validateAcceleratedComputing(ac map[string][]*models.ResourceQuotaItem) error {
	for xpuType, items := range ac {
		if xpuType == "" {
			return stderr.Errorf("XPU type cannot be empty")
		}

		for _, item := range items {
			if err := v.ValidateQuotaItem(item); err != nil {
				return stderr.Errorf("invalid XPU quota item for type %s: %v", xpuType, err)
			}
		}
	}

	return nil
}

func (v *QuotaValidator) validateQueueQuota(queue *models.QueueResourceQuota) error {
	if err := v.validateQueueSpec(&queue.Default, "default"); err != nil {
		return err
	}

	if err := v.validateQueueSpec(&queue.Task, "task"); err != nil {
		return err
	}

	if err := v.validateQueueSpec(&queue.Infer, "infer"); err != nil {
		return err
	}

	return nil
}

func (v *QuotaValidator) validateQueueSpec(spec *models.QueueResourceQuotaSpec, queueType string) error {
	if err := v.ValidateQuotaItem(&spec.CPU); err != nil {
		return stderr.Errorf("invalid CPU quota for %s queue: %v", queueType, err)
	}

	if err := v.ValidateQuotaItem(&spec.Memory); err != nil {
		return stderr.Errorf("invalid Memory quota for %s queue: %v", queueType, err)
	}

	if err := v.validateAcceleratedComputing(spec.AcceleratedComputing); err != nil {
		return stderr.Errorf("invalid XPU quota for %s queue: %v", queueType, err)
	}

	return nil
}

func (v *QuotaValidator) validateWeightMode(spec *models.ResourceQuotaSpec) error {
	if !spec.WeightMode {
		return nil
	}

	if spec.CPU.Weight <= 0 {
		return stderr.Errorf("CPU weight must be greater than 0 in weight mode")
	}

	if spec.Memory.Weight <= 0 {
		return stderr.Errorf("Memory weight must be greater than 0 in weight mode")
	}

	return nil
}

func (v *QuotaValidator) validateQuantity(quantity string) error {
	if quantity == "" {
		return nil
	}

	_, err := resource.ParseQuantity(quantity)
	if err != nil {
		return stderr.Errorf("invalid quantity format: %s", quantity)
	}

	return nil
}

func (v *QuotaValidator) validateQuotaItemType(itemType models.QuotaItemType) error {
	switch itemType {
	case models.QuotaItemTypeCPU, models.QuotaItemTypeMem, models.QuotaItemTypeXPUCores, models.QuotaItemTypeXPUMem:
		return nil
	case "":
		return stderr.Errorf("quota item type cannot be empty")
	default:
		return stderr.Errorf("invalid quota item type: %s", itemType)
	}
}

func (v *QuotaValidator) compareQuantities(requested, limit, resourceName string) error {
	if requested == "" || limit == "" {
		return nil
	}

	reqQuantity, err := resource.ParseQuantity(requested)
	if err != nil {
		return stderr.Errorf("invalid requested quantity for %s: %v", resourceName, err)
	}

	limitQuantity, err := resource.ParseQuantity(limit)
	if err != nil {
		return stderr.Errorf("invalid limit quantity for %s: %v", resourceName, err)
	}

	if reqQuantity.Cmp(limitQuantity) > 0 {
		return stderr.Errorf("%s quota %s exceeds limit %s", resourceName, requested, limit)
	}

	return nil
}

func (v *QuotaValidator) ValidateResourceName(name string) error {
	if name == "" {
		return stderr.Errorf("resource name cannot be empty")
	}

	if strings.Contains(name, " ") {
		return stderr.Errorf("resource name cannot contain spaces")
	}

	return nil
}
