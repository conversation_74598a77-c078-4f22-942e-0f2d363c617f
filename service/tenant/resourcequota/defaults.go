package resourcequota

import (
	corev1 "k8s.io/api/core/v1"
	"transwarp.io/applied-ai/central-auth-service/conf"
	"transwarp.io/applied-ai/central-auth-service/models"
)

type DefaultConfigProvider struct{}

func NewDefaultConfigProvider() *DefaultConfigProvider {
	return &DefaultConfigProvider{}
}

func (p *DefaultConfigProvider) GetDefaultQuota() *models.TenantResourceQuota {
	return &models.TenantResourceQuota{
		QuotaName: DefaultQuotaName,
		Hard:      *p.GetDefaultQuotaSpec(),
		Used:      *p.GetZeroQuotaSpec(),
		Limits:    *p.GetDefaultLimitsSpec(),
		QuotaType: models.QuotaTypeFixed,
	}
}

func (p *DefaultConfigProvider) GetDefaultQuotaSpec() *models.ResourceQuotaSpec {
	defaultQuota := conf.C.Tenant.DefaultQuota

	spec := &models.ResourceQuotaSpec{
		Pods:                 defaultQuota.Pods,
		Bandwidth:            defaultQuota.Bandwidth,
		Knowl:                defaultQuota.KnowledgeBaseStorage,
		KnowledgeBaseStorage: defaultQuota.KnowledgeBaseStorage,
		FileStorage:          defaultQuota.FileStorage,

		LimitsCpu:    defaultQuota.LimitsCpu,
		LimitsMemory: defaultQuota.LimitsMemory,
		Gpu:          defaultQuota.Gpu,
		GpuMemory:    defaultQuota.GpuMemory,

		CPU: models.ResourceQuotaItem{
			Name:          corev1.ResourceCPU,
			NominalQuota:  defaultQuota.LimitsCpu,
			QuotaItemType: models.QuotaItemTypeCPU,
			Unit:          UnitCPU,
		},
		Memory: models.ResourceQuotaItem{
			Name:          corev1.ResourceMemory,
			NominalQuota:  defaultQuota.LimitsMemory,
			QuotaItemType: models.QuotaItemTypeMem,
			Unit:          UnitMemory,
		},
		AcceleratedComputing: make(map[string][]*models.ResourceQuotaItem),
		Queue:                &models.QueueResourceQuota{},
	}

	models.ApplyDefaultQuotaForQueue(spec)

	return spec
}

func (p *DefaultConfigProvider) GetZeroQuotaSpec() *models.ResourceQuotaSpec {
	return &models.ResourceQuotaSpec{
		LimitsCpu:    Zero,
		LimitsMemory: ZeroGi,
		Pods:         Zero,
		Gpu:          Zero,
		GpuMemory:    ZeroGi,
		Bandwidth:    ZeroGi,
		Knowl:        ZeroGi,
		FileStorage:  ZeroGi,

		CPU: models.ResourceQuotaItem{
			Name:          corev1.ResourceCPU,
			NominalQuota:  Zero,
			QuotaItemType: models.QuotaItemTypeCPU,
			Unit:          UnitCPU,
		},
		Memory: models.ResourceQuotaItem{
			Name:          corev1.ResourceMemory,
			NominalQuota:  ZeroGi,
			QuotaItemType: models.QuotaItemTypeMem,
			Unit:          UnitMemory,
		},
		AcceleratedComputing: make(map[string][]*models.ResourceQuotaItem),
		Queue:                &models.QueueResourceQuota{},
	}
}

func (p *DefaultConfigProvider) GetDefaultLimitsSpec() *models.ResourceQuotaSpec {
	limits := &models.ResourceQuotaSpec{
		LimitsCpu:            DefaultLimitsCPU,
		LimitsMemory:         DefaultLimitsMemory,
		Pods:                 DefaultPods,
		RequestsCpu:          DefaultRequestsCPU,
		RequestsMemory:       DefaultRequestsMemory,
		RequestsStorage:      DefaultRequestsStorage,
		Bandwidth:            DefaultBandwidth,
		EgressBandwidth:      DefaultEgressBandwidth,
		IngressBandwidth:     DefaultIngressBandwidth,
		Gpu:                  DefaultGPU,
		GpuMemory:            DefaultGPUMemory,
		Knowl:                DefaultKnowledgeStorage,
		KnowledgeBaseStorage: DefaultKnowledgeStorage,
		FileStorage:          DefaultFileStorage,

		CPU: models.ResourceQuotaItem{
			Name:          corev1.ResourceCPU,
			NominalQuota:  DefaultLimitsCPU,
			QuotaItemType: models.QuotaItemTypeCPU,
			Unit:          UnitCPU,
		},
		Memory: models.ResourceQuotaItem{
			Name:          corev1.ResourceMemory,
			NominalQuota:  DefaultLimitsMemory,
			QuotaItemType: models.QuotaItemTypeMem,
			Unit:          UnitMemory,
		},
		AcceleratedComputing: make(map[string][]*models.ResourceQuotaItem),
		Queue:                &models.QueueResourceQuota{},
	}

	limits.Queue.Task.CPU = limits.CPU
	limits.Queue.Infer.CPU = limits.CPU
	limits.Queue.Default.CPU = limits.CPU
	limits.Queue.Task.Memory = limits.Memory
	limits.Queue.Infer.Memory = limits.Memory
	limits.Queue.Default.Memory = limits.Memory
	limits.Queue.Task.AcceleratedComputing = limits.AcceleratedComputing
	limits.Queue.Infer.AcceleratedComputing = limits.AcceleratedComputing
	limits.Queue.Default.AcceleratedComputing = limits.AcceleratedComputing

	return limits
}

func (p *DefaultConfigProvider) GetDefaultXPUQuotaItems() map[string][]*models.ResourceQuotaItem {
	xpuItems := make(map[string][]*models.ResourceQuotaItem)

	// Nvidia GPU
	// xpuItems[XPUTypeNvidia] = []*models.ResourceQuotaItem{
	// 	{
	// 		Name:          corev1.ResourceName(NvidiaGPU),
	// 		NominalQuota:  Zero,
	// 		QuotaItemType: models.QuotaItemTypeXPUCores,
	// 		Unit:          UnitGPU,
	// 	},
	// 	{
	// 		Name:          corev1.ResourceName(NvidiaGPUMemory),
	// 		NominalQuota:  ZeroGi,
	// 		QuotaItemType: models.QuotaItemTypeXPUMem,
	// 		Unit:          UnitMemory,
	// 	},
	// 	{
	// 		Name:          corev1.ResourceName(NvidiaGPUCores),
	// 		NominalQuota:  Zero,
	// 		QuotaItemType: models.QuotaItemTypeXPUCores,
	// 		Unit:          UnitGPU,
	// 	},
	// }

	return xpuItems
}
