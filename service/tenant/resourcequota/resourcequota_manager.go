package resourcequota

import (
	"context"
	"fmt"
	"os"
	"strconv"
	"time"

	xpu "transwarp.io/aip/llmops-common/pkg/crd"
	knm_v1 "transwarp.io/applied-ai/kube-nodexpu-manager/apis/resources/v1alpha1"

	"gopkg.in/yaml.v3"
	corev1 "k8s.io/api/core/v1"
	k8serrors "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/tools/cache"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/conf"
	"transwarp.io/applied-ai/central-auth-service/models"
	helm "transwarp.io/applied-ai/central-auth-service/service/tenant/helm"
	k8s "transwarp.io/applied-ai/central-auth-service/service/tenant/kubernetes"
	"transwarp.io/applied-ai/central-auth-service/service/tenant/util"
)

var (
	instance *ResourceQuotaManager
)

// tmpl for render helm values.yaml
// type LlmopsQueueHelmValues struct {
// 	Resourcequota LlmopsQueueResourceQuota `json:"resourcequota" yaml:"resourcequota"`
// }
//
// type LlmopsQueueResourceQuota struct {
// 	Cohort models.ResourceQuotaSpec `json:"cohort" yaml:"cohort"`
// }

type ResourceQuotaManager struct {
	defaultQuota *models.TenantResourceQuota

	nxpuClient *NXPUClient

	kclient     *k8s.KClientset
	configWatch chan *corev1.ConfigMap
	stopCh      chan struct{}

	// xpugroup
	nxgWatch chan *knm_v1.NodeXpuGroup
}

func GetResourceQuotaManager() (*ResourceQuotaManager, error) {
	return instance, nil
}

func initResourceQuotaManager() (*ResourceQuotaManager, error) {
	kclient, err := k8s.NewKClientset()
	if err != nil {
		return nil, err
	}

	manager := &ResourceQuotaManager{
		kclient:     kclient,
		configWatch: make(chan *corev1.ConfigMap),
		stopCh:      make(chan struct{}),

		nxgWatch: make(chan *knm_v1.NodeXpuGroup),

		nxpuClient: NewXpuClient(),
	}

	manager.StartResourceQuotaManagerLeaderElection(context.Background())

	if err := manager.WatchResourceQuotaCreateOrUpdate(); err != nil {
		return nil, err
	}

	if err := manager.WatchXpuGroupUpdate(); err != nil {
		return nil, err
	}

	return manager, nil
}

func (qm *ResourceQuotaManager) StartResourceQuotaManagerLeaderElection(ctx context.Context) error {
	onStartedLeading := func(ctx context.Context) {
	}
	onStoppedLeading := func() {
	}
	onNewLeader := func(identity string) {
	}

	leConfig := util.NewDefaultLeaderElectionConfig(
		util.DefaultLeaderElectionID,
		util.GetCurrentNamespaceOrDefault("llmops"),
		util.GetCurrentPodNameOrDefault("cas"),
		qm.kclient.KubeClient,
		onStartedLeading,
		onStoppedLeading,
		onNewLeader,
		util.DefaultLeaderElectionLeaseDuration,
		util.DefaultLeaderElectionRenewDeadline,
		util.DefaultLeaderElectionRetryPeriod,
	)

	if err := util.StartLeaderElection(ctx, leConfig); err != nil {
		return fmt.Errorf("failed to start task handler leader election: %w", err)
	}

	return nil
}

func (qm *ResourceQuotaManager) getConfigMapName(namespace string) string {
	return fmt.Sprintf("%s-quota", namespace)
}

func (qm *ResourceQuotaManager) getConfigMapNameLabels() map[string]string {
	return util.WithManagedByLabels(map[string]string{
		labelKeyResourceQuotaSpec: labelValueResourceQuotaSpec,
	})
}

func (qm *ResourceQuotaManager) CreateOrUpgradeResourceQuota(namespace string, spec *models.ResourceQuotaSpec) error {
	specYaml, err := yaml.Marshal(spec)
	if err != nil {
		return err
	}

	cmClient := qm.kclient.KubeClient.CoreV1().ConfigMaps(namespace)

	name := qm.getConfigMapName(namespace)
	cm, err := cmClient.Get(context.Background(), name, metav1.GetOptions{})

	if k8serrors.IsNotFound(err) {
		// Create new ConfigMap if not found
		cm = &corev1.ConfigMap{
			ObjectMeta: metav1.ObjectMeta{
				Name:      name,
				Namespace: namespace,
				Labels:    qm.getConfigMapNameLabels(),
			},
			Data: map[string]string{
				resourceQuotaConfigKey: string(specYaml),
			},
		}

		_, err = cmClient.Create(context.Background(), cm, metav1.CreateOptions{})
		if err != nil {
			return err
		}
		stdlog.Infof("Created resource quota ConfigMap for namespace %s", namespace)
		return nil
	}

	if err != nil {
		return err
	}

	// Update existing ConfigMap
	if cm.Data == nil {
		cm.Data = make(map[string]string)
	}
	cm.Data[resourceQuotaConfigKey] = string(specYaml)

	_, err = cmClient.Update(context.Background(), cm, metav1.UpdateOptions{})
	if err != nil {
		return err
	}

	stdlog.Infof("Updated resource quota ConfigMap for namespace %s", namespace)
	return nil
}

func (qm *ResourceQuotaManager) WatchResourceQuotaCreateOrUpdate() error {
	informer := qm.kclient.K8sInformerClient.ConfigMapInformer
	// Add event handler with label filtering
	informer.AddEventHandler(cache.FilteringResourceEventHandler{
		FilterFunc: func(obj interface{}) bool {
			cm, ok := obj.(*corev1.ConfigMap)
			if !ok {
				return false
			}
			return util.ContainLabels(cm.Labels, qm.getConfigMapNameLabels())
		},
		Handler: cache.ResourceEventHandlerFuncs{
			AddFunc: func(obj interface{}) {
				cm := obj.(*corev1.ConfigMap)
				qm.configWatch <- cm
			},
			UpdateFunc: func(oldObj, newObj interface{}) {
				oldCM := newObj.(*corev1.ConfigMap)
				newCM := newObj.(*corev1.ConfigMap)
				if oldCM.ResourceVersion == newCM.ResourceVersion {
					return
				}
				qm.configWatch <- newCM
			},
		},
	})

	if !cache.WaitForCacheSync(qm.stopCh, informer.HasSynced) {
		stdlog.Errorf("Failed to sync resource quota ConfigMap informer.")
	}

	go func(ctx context.Context) {
		for {
			select {
			case cm := <-qm.configWatch:
				if util.IsLeader() {
					if err := qm.installOrUpgradeHelmRelease(cm); err != nil {
						stdlog.Infof("Failed to install or upgrade resource quota using helm: %v", err)
					}
				}
			case <-ctx.Done():
				return
			}
		}
	}(context.Background())

	return nil
}

func (qm *ResourceQuotaManager) Cleanup() {
	if qm.stopCh != nil {
		close(qm.stopCh)
	}
}

func (qm *ResourceQuotaManager) installOrUpgradeHelmRelease(cm *corev1.ConfigMap) error {
	specYaml, ok := cm.Data[resourceQuotaConfigKey]
	if !ok {
		return stderr.Errorf("Resource quota spec not found in ConfigMap")
	}

	namespace := cm.Namespace

	spec := &models.ResourceQuotaSpec{}
	if err := yaml.Unmarshal([]byte(specYaml), spec); err != nil {
		return err
	}

	// cal ac mem count by unit
	applyACMem := func(ac map[string][]*models.ResourceQuotaItem) {
		for _, v := range ac {
			for _, u := range v {
				totalMem := resource.MustParse(u.NominalQuota)
				if u.Unit == "" {
					continue
				}
				unit := resource.MustParse(u.Unit)
				count := totalMem.Value() / unit.Value()
				u.NominalQuota = strconv.FormatInt(count, 10)
			}
		}
	}
	applyACMem(spec.AcceleratedComputing)
	applyACMem(spec.Queue.Task.AcceleratedComputing)
	applyACMem(spec.Queue.Infer.AcceleratedComputing)
	applyACMem(spec.Queue.Default.AcceleratedComputing)

	recalculateQueueQuotaIfEnableWeightMode := func(spec *models.ResourceQuotaSpec) {
		if spec.Queue == nil {
			return
		}

		if !spec.WeightMode {
			return
		}

		// TODO default queue weight is 1.0, user all tenant resource
		w := 1.0
		spec.Queue.Default.CPU.NominalQuota, _ = util.CalculateByWeight(spec.CPU.NominalQuota, w)
		spec.Queue.Default.Memory.NominalQuota, _ = util.CalculateByWeight(spec.Memory.NominalQuota, w)
		for _, v := range spec.Queue.Default.AcceleratedComputing {
			for _, u := range v {
				u.NominalQuota, _ = util.CalculateByWeight(u.NominalQuota, w)
			}
		}

		// infer
		w = spec.CPU.Weight / 100.0 * spec.Queue.Infer.CPU.Weight / 100.0
		spec.Queue.Infer.CPU.NominalQuota, _ = util.CalculateByWeight(spec.CPU.NominalQuota, w)
		w = spec.Memory.Weight / 100.0 * spec.Queue.Infer.Memory.Weight / 100.0
		spec.Queue.Infer.Memory.NominalQuota, _ = util.CalculateByWeight(spec.Memory.NominalQuota, w)
		for u, v := range spec.Queue.Infer.AcceleratedComputing {
			uStr := string(u)
			for uu, vv := range v {
				uuStr := string(uu)
				for x, y := range spec.AcceleratedComputing {
					if uStr == string(x) {
						for xx, yy := range y {
							if uStr == string(x) && uuStr == string(xx) {
								w = yy.Weight / 100.0 * vv.Weight / 100.0
								vv.NominalQuota, _ = util.CalculateByWeight(vv.NominalQuota, w)
								break
							}
						}
						break
					}
				}
			}
		}
		// task
		w = spec.CPU.Weight / 100.0 * spec.Queue.Task.CPU.Weight / 100.0
		spec.Queue.Task.CPU.NominalQuota, _ = util.CalculateByWeight(spec.CPU.NominalQuota, w)
		w = spec.Memory.Weight / 100.0 * spec.Queue.Task.Memory.Weight / 100.0
		spec.Queue.Task.Memory.NominalQuota, _ = util.CalculateByWeight(spec.Memory.NominalQuota, w)
		for u, v := range spec.Queue.Task.AcceleratedComputing {
			uStr := string(u)
			for uu, vv := range v {
				uuStr := string(uu)
				for x, y := range spec.AcceleratedComputing {
					if uStr == string(x) {
						for xx, yy := range y {
							if uStr == string(x) && uuStr == string(xx) {
								w = yy.Weight / 100.0 * vv.Weight / 100.0
								vv.NominalQuota, _ = util.CalculateByWeight(vv.NominalQuota, w)
								break
							}
						}
						break
					}
				}
			}
		}
		// if not weight mode, return directly
		w = spec.CPU.Weight / 100.0
		spec.CPU.NominalQuota, _ = util.CalculateByWeight(spec.CPU.NominalQuota, w)
		w = spec.Memory.Weight / 100.0
		spec.Memory.NominalQuota, _ = util.CalculateByWeight(spec.Memory.NominalQuota, w)
		for _, v := range spec.AcceleratedComputing {
			for _, u := range v {
				w := u.Weight / 100.0
				u.NominalQuota, _ = util.CalculateByWeight(u.NominalQuota, w)
			}
		}
	}
	recalculateQueueQuotaIfEnableWeightMode(spec)

	// apply queue nominal quota and borrowing limit
	applyQueueQuotaBorrowingLimit := func(spec *models.QueueResourceQuotaSpec) {
		spec.CPU.BorrowingLimit = spec.CPU.NominalQuota
		spec.Memory.BorrowingLimit = spec.Memory.NominalQuota
		spec.CPU.NominalQuota = util.Zero
		spec.Memory.NominalQuota = util.ZeroGi
		for _, v := range spec.AcceleratedComputing {
			for _, u := range v {
				u.BorrowingLimit = u.NominalQuota
				u.NominalQuota = util.Zero
			}
		}
	}
	applyQueueQuotaBorrowingLimit(&spec.Queue.Default)
	applyQueueQuotaBorrowingLimit(&spec.Queue.Infer)
	applyQueueQuotaBorrowingLimit(&spec.Queue.Task)

	valuesPath, err := qm.renderValues(namespace, spec)
	if err != nil {
		stdlog.Errorf("Failed to render values for llmops-queue in %s: %+v", namespace, err)
		return err
	}

	return helm.InstallLlmopsQueue(namespace, nil, []string{valuesPath}...)
}

func (qm *ResourceQuotaManager) renderValues(namespace string, spec *models.ResourceQuotaSpec) (string, error) {
	values := LlmopsQueueHelmValues{
		Resourcequota: LlmopsQueueResourceQuota{
			Cohort: *spec,
		},
	}

	valuesPath := fmt.Sprintf("/tmp/%s-quota-values-%s.yaml", namespace,
		func() string {
			now := time.Now()
			year, month, day := now.Date()

			return fmt.Sprintf("%d-%02d-%02d", year, month, day)
		}())

	file, err := os.Create(valuesPath)
	if err != nil {
		stdlog.Errorf("Failed to create values file for namespace %s: %v", namespace, err)
		return "", err
	}
	defer file.Close()

	fileEncoder := yaml.NewEncoder(file)
	fileEncoder.SetIndent(2)
	if err = fileEncoder.Encode(values); err != nil {
		stdlog.Errorf("Failed to encode values to YAML for namespace %s: %v", namespace, err)
		return "", err
	}

	if err = fileEncoder.Close(); err != nil {
		stdlog.Errorf("Failed to close encoder for namespace %s: %v", namespace, err)
		return "", err
	}

	stdlog.Infof("Successfully rendered values file for namespace %s at %s", namespace, valuesPath)
	return valuesPath, nil
}

func (qm *ResourceQuotaManager) getResourcequota(namespace string) (*models.ResourceQuotaSpec, error) {
	lister := qm.kclient.K8sInformerClient.ConfigMapLister

	name := qm.getConfigMapName(namespace)

	cm, err := lister.ConfigMaps(namespace).Get(name)
	if err != nil {
		return nil, err
	}
	if cm == nil {
		return nil, stderr.Errorf("Resource quota configmap not found")
	}

	specYaml, ok := cm.Data[resourceQuotaConfigKey]
	if !ok {
		return nil, stderr.Errorf("Resource quota spec not found in ConfigMap")
	}

	spec := &models.ResourceQuotaSpec{}
	if err := yaml.Unmarshal([]byte(specYaml), spec); err != nil {
		return nil, err
	}

	return spec, nil
}

func (qm *ResourceQuotaManager) GetTenantResourcequota(namespace string) (*models.TenantResourceQuota, error) {
	spec, err := qm.getResourcequota(namespace)
	if err != nil {
		return nil, err
	}
	spec.XPUTypeCount = len(spec.AcceleratedComputing)
	return &models.TenantResourceQuota{
		QuotaType: spec.QuotaType,
		Hard:      *spec,
	}, nil
}

func (qm *ResourceQuotaManager) GetDefaultQuota() *models.TenantResourceQuota {
	if qm.defaultQuota == nil {
		defaultQuota := &models.TenantResourceQuota{
			QuotaName: "default",
			Hard: models.ResourceQuotaSpec{
				Pods: conf.C.Tenant.DefaultQuota.Pods,

				Bandwidth: conf.C.Tenant.DefaultQuota.Bandwidth,

				Knowl:                conf.C.Tenant.DefaultQuota.KnowledgeBaseStorage,
				KnowledgeBaseStorage: conf.C.Tenant.DefaultQuota.KnowledgeBaseStorage,
				FileStorage:          conf.C.Tenant.DefaultQuota.FileStorage,

				// compatiable v1
				LimitsCpu:    conf.C.Tenant.DefaultQuota.LimitsCpu,
				LimitsMemory: conf.C.Tenant.DefaultQuota.LimitsMemory,
				Gpu:          conf.C.Tenant.DefaultQuota.Gpu,
				GpuMemory:    conf.C.Tenant.DefaultQuota.GpuMemory,

				CPU: models.ResourceQuotaItem{
					Name:          corev1.ResourceCPU,
					NominalQuota:  conf.C.Tenant.DefaultQuota.LimitsCpu,
					QuotaItemType: models.QuotaItemTypeCPU,
					Unit:          "1",
				},
				Memory: models.ResourceQuotaItem{
					Name:          corev1.ResourceMemory,
					NominalQuota:  conf.C.Tenant.DefaultQuota.LimitsMemory,
					QuotaItemType: models.QuotaItemTypeMem,
					Unit:          "1Gi",
				},
				AcceleratedComputing: make(map[string][]*models.ResourceQuotaItem),
				Queue:                &models.QueueResourceQuota{},
			},
		}
		defaultQuota.Hard.Queue.Task.CPU = defaultQuota.Hard.CPU
		defaultQuota.Hard.Queue.Task.CPU.NominalQuota = util.Zero
		defaultQuota.Hard.Queue.Infer.CPU = defaultQuota.Hard.CPU
		defaultQuota.Hard.Queue.Infer.CPU.NominalQuota = util.Zero
		defaultQuota.Hard.Queue.Default.CPU = defaultQuota.Hard.CPU
		defaultQuota.Hard.Queue.Default.CPU.NominalQuota = util.Zero
		defaultQuota.Hard.Queue.Task.Memory = defaultQuota.Hard.Memory
		defaultQuota.Hard.Queue.Task.Memory.NominalQuota = util.ZeroGi
		defaultQuota.Hard.Queue.Infer.Memory = defaultQuota.Hard.Memory
		defaultQuota.Hard.Queue.Infer.Memory.NominalQuota = util.ZeroGi
		defaultQuota.Hard.Queue.Default.Memory = defaultQuota.Hard.Memory
		defaultQuota.Hard.Queue.Default.Memory.NominalQuota = util.ZeroGi

		spec, err := qm.getClusterTotalAcceleratedComputingResourceQuotaSpec()
		if err != nil {
			stdlog.Errorf("Get cluster total accelerated computing resource quota spec failed, error: %s.", err)
		} else {
			for k, v := range spec.AcceleratedComputing {
				for _, u := range v {
					u.BorrowingLimit = ""
					u.LendingLimit = ""
				}
				defaultQuota.Hard.AcceleratedComputing[k] = v
			}
		}

		defaultQuota.Hard.Queue.Task.AcceleratedComputing = defaultQuota.Hard.AcceleratedComputing
		for _, v := range defaultQuota.Hard.Queue.Task.AcceleratedComputing {
			for _, u := range v {
				u.NominalQuota = util.ZeroWithUnit(u.NominalQuota)
				u.BorrowingLimit = ""
				u.LendingLimit = ""
			}
		}
		defaultQuota.Hard.Queue.Infer.AcceleratedComputing = defaultQuota.Hard.AcceleratedComputing
		for _, v := range defaultQuota.Hard.Queue.Infer.AcceleratedComputing {
			for _, u := range v {
				u.NominalQuota = util.ZeroWithUnit(u.NominalQuota)
				u.BorrowingLimit = ""
				u.LendingLimit = ""
			}
		}
		defaultQuota.Hard.Queue.Default.AcceleratedComputing = defaultQuota.Hard.AcceleratedComputing
		for _, v := range defaultQuota.Hard.Queue.Default.AcceleratedComputing {
			for _, u := range v {
				u.NominalQuota = util.ZeroWithUnit(u.NominalQuota)
				u.BorrowingLimit = ""
				u.LendingLimit = ""
			}
		}

		qm.defaultQuota = defaultQuota
	}

	limits, err := qm.GetClusterResourceLimits()
	if err != nil {
		stdlog.Errorf("Get cluster resource limits failed, error: %s.", err)
		qm.defaultQuota.Limits = *qm.getDefaultLimitsResourceQuotaSpec()
	} else {
		qm.defaultQuota.Limits = *limits
	}

	qm.defaultQuota.Hard.XPUTypeCount = len(qm.defaultQuota.Hard.AcceleratedComputing)

	return qm.defaultQuota
}

func (qm *ResourceQuotaManager) getDefaultLimitsResourceQuotaSpec() *models.ResourceQuotaSpec {
	limits := &models.ResourceQuotaSpec{
		LimitsCpu:            "100000",
		LimitsMemory:         "100000Gi",
		Pods:                 "100000",
		RequestsCpu:          "100000",
		RequestsMemory:       "100000Gi",
		RequestsStorage:      "10000000Gi",
		Bandwidth:            "1000Gi",
		EgressBandwidth:      "1000Gi",
		IngressBandwidth:     "1000Gi",
		Gpu:                  "100000",
		GpuMemory:            "100000Gi",
		Knowl:                "100000Gi",
		KnowledgeBaseStorage: "100000Gi",
		FileStorage:          "100000Gi",

		CPU: models.ResourceQuotaItem{
			Name:          corev1.ResourceCPU,
			NominalQuota:  "100000",
			QuotaItemType: models.QuotaItemTypeCPU,
		},
		Memory: models.ResourceQuotaItem{
			Name:          corev1.ResourceMemory,
			NominalQuota:  "100000Gi",
			QuotaItemType: models.QuotaItemTypeMem,
		},
		AcceleratedComputing: make(map[string][]*models.ResourceQuotaItem),
		Queue:                &models.QueueResourceQuota{},
	}
	limits.Queue.Task.CPU = limits.CPU
	limits.Queue.Infer.CPU = limits.CPU
	limits.Queue.Default.CPU = limits.CPU
	limits.Queue.Task.Memory = limits.Memory
	limits.Queue.Infer.Memory = limits.Memory
	limits.Queue.Default.Memory = limits.Memory
	limits.Queue.Task.AcceleratedComputing = limits.AcceleratedComputing
	limits.Queue.Infer.AcceleratedComputing = limits.AcceleratedComputing
	limits.Queue.Default.AcceleratedComputing = limits.AcceleratedComputing

	return limits
}

func (qm *ResourceQuotaManager) GetClusterResourceLimits() (*models.ResourceQuotaSpec, error) {
	limits := qm.getDefaultLimitsResourceQuotaSpec()
	totalCpu, totalMemory, err := qm.kclient.CalClusterTotalCPUAndMemory()
	if err != nil {
		return nil, err
	}
	limits.LimitsCpu = totalCpu
	limits.LimitsMemory = totalMemory
	limits.CPU = models.ResourceQuotaItem{
		Name:          corev1.ResourceCPU,
		NominalQuota:  totalCpu,
		QuotaItemType: models.QuotaItemTypeCPU,
	}
	limits.Memory = models.ResourceQuotaItem{
		Name:          corev1.ResourceMemory,
		NominalQuota:  totalMemory,
		QuotaItemType: models.QuotaItemTypeMem,
	}

	limits.Queue.Task.CPU = limits.CPU
	limits.Queue.Infer.CPU = limits.CPU
	limits.Queue.Default.CPU = limits.CPU
	limits.Queue.Task.Memory = limits.Memory
	limits.Queue.Infer.Memory = limits.Memory
	limits.Queue.Default.Memory = limits.Memory

	// set accelerated computing
	spec, err := qm.getClusterTotalAcceleratedComputingResourceQuotaSpec()
	if err != nil {
		if limits.AcceleratedComputing == nil {
			limits.AcceleratedComputing = make(map[string][]*models.ResourceQuotaItem, 0)
		}
	} else {
		limits.AcceleratedComputing = spec.AcceleratedComputing
	}
	limits.Queue.Task.AcceleratedComputing = limits.AcceleratedComputing
	limits.Queue.Infer.AcceleratedComputing = limits.AcceleratedComputing
	limits.Queue.Default.AcceleratedComputing = limits.AcceleratedComputing

	return limits, nil
}

func (qm *ResourceQuotaManager) getClusterTotalAcceleratedComputingResourceQuotaSpec() (*models.ResourceQuotaSpec, error) {
	spec := &models.ResourceQuotaSpec{
		AcceleratedComputing: make(map[string][]*models.ResourceQuotaItem),
	}

	ac, err := qm.nxpuClient.LimitACResources(context.Background())
	if err != nil {
		return spec, nil
	}
	spec.AcceleratedComputing = ac

	return spec, nil
}

func GetDefaultResourceQuotaV2() *models.TenantResourceQuota {
	manager, err := GetResourceQuotaManager()
	if err != nil {
		stdlog.Errorf("Failed to get resource quota manager: %v", err)
		return &models.TenantResourceQuota{}
	}
	return manager.GetDefaultQuota()
}

func (qm *ResourceQuotaManager) WatchXpuGroupUpdate() error {
	informer := xpu.Rg.GroupInformer.Informer()

	informer.AddEventHandler(cache.FilteringResourceEventHandler{
		FilterFunc: func(obj interface{}) bool {
			nxg, ok := obj.(*knm_v1.NodeXpuGroup)
			if !ok {
				return false
			}
			return util.ContainLabels(nxg.Labels, util.GetResourceGroupManagedNamespaceLabels())
		},
		Handler: cache.ResourceEventHandlerFuncs{
			UpdateFunc: func(oldObj, newObj interface{}) {
				oldNxg := oldObj.(*knm_v1.NodeXpuGroup)
				newNxg := newObj.(*knm_v1.NodeXpuGroup)
				if oldNxg.ResourceVersion == newNxg.ResourceVersion {
					return
				}
				qm.nxgWatch <- newNxg
			},
		},
	})

	if !cache.WaitForCacheSync(qm.stopCh, informer.HasSynced) {
		stdlog.Errorf("Failed to sync resource quota ConfigMap informer.")
	}

	go func(ctx context.Context) {
		for {
			select {
			case nxg := <-qm.nxgWatch:
				if util.IsLeader() {
					if err := qm.updateResourceQuotaWhenXpuGroupUpdate(nxg); err != nil {
						stdlog.Errorf("Failed to update resource quota when watch nxg update: %v", err)
					}
				}
			case <-ctx.Done():
				return
			}
		}
	}(context.Background())
	return nil
}

func (qm *ResourceQuotaManager) updateResourceQuotaWhenXpuGroupUpdate(nxg *knm_v1.NodeXpuGroup) error {
	newSpecs, err := qm.nxpuClient.RecalculateNamespaceResourceQuotaEffectByNXG(nxg.GetName())
	if err != nil {
		return err
	}
	for ns, newSpec := range newSpecs {
		oldSepc, err := qm.getResourcequota(ns)
		if err != nil {
			return err
		}
		if oldSepc.QuotaType != models.QuotaTypeDynamic {
			continue
		}

		oldSepc.ApplyNewQuota(&newSpec)
		if err := qm.CreateOrUpgradeResourceQuota(ns, oldSepc); err != nil {
			return err
		}
	}
	return nil
}

func (qm *ResourceQuotaManager) NxgToResourceQuotaSpec(nxgIDs []string) (*models.ResourceQuotaSpec, error) {
	spec, err := qm.nxpuClient.NXPUGroupToResourceQuotaSpec(nxgIDs)
	if err != nil {
		return &models.ResourceQuotaSpec{}, err
	}

	return spec, nil
}
