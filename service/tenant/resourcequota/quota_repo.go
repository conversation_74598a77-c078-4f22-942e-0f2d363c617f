package resourcequota

import (
	"context"

	"gopkg.in/yaml.v3"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/models"
)

type QuotaRepoImpl struct {
	configRepo ConfigRepo
}

func NewQuotaRepo() QuotaRepo {
	return &QuotaRepoImpl{
		configRepo: NewConfigMapRepo(),
	}
}

func (r *QuotaRepoImpl) GetQuota(ctx context.Context, namespace string) (*models.ResourceQuotaSpec, error) {
	config, err := r.configRepo.GetConfig(ctx, namespace)
	if err != nil {
		stdlog.Errorf("Failed to get quota for namespace %s: %v", namespace, err)
		return nil, err
	}

	if config == nil {
		return nil, stderr.<PERSON><PERSON><PERSON>("Resource quota configmap not found")
	}

	specYaml, ok := config[resourceQuotaConfigKey]
	if !ok {
		return nil, stderr.Errorf("Resource quota spec not found in ConfigMap")
	}

	spec := &models.ResourceQuotaSpec{}
	if err := yaml.Unmarshal([]byte(specYaml), spec); err != nil {
		return nil, err
	}

	return spec, nil
}

func (r *QuotaRepoImpl) SaveQuota(ctx context.Context, namespace string, spec *models.ResourceQuotaSpec) error {
	specYaml, err := yaml.Marshal(spec)
	if err != nil {
		return err
	}

	data := make(map[string]string)
	data[resourceQuotaConfigKey] = string(specYaml)

	if err := r.configRepo.SaveConfig(ctx, namespace, data); err != nil {
		return err
	}

	stdlog.Infof("Updated resource quota ConfigMap for namespace %s", namespace)
	return nil
}

func (r *QuotaRepoImpl) DeleteQuota(ctx context.Context, namespace string) error {
	return r.configRepo.DeleteConfig(ctx, namespace)
}
