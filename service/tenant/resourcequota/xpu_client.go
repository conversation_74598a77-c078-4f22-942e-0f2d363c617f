package resourcequota

import (
	"context"
	"sync"
	"time"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	"k8s.io/apimachinery/pkg/labels"
	xpu "transwarp.io/aip/llmops-common/pkg/crd"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/models"
	k8s "transwarp.io/applied-ai/central-auth-service/service/tenant/kubernetes"
	"transwarp.io/applied-ai/central-auth-service/service/tenant/util"
	knm_v1 "transwarp.io/applied-ai/kube-nodexpu-manager/apis/resources/v1alpha1"
)

// xpu.kubernetes.io/resource-type-key: nvidia | hygon | iluvatar | cambricon | mlu | metax | mthreads
// xpu.kubernetes.io/cores-key: "cores.llmops.transwarp.io/iluvatar"
// xpu.kubernetes.io/mem-key: "mem.llmops.transwarp.io/iluvatar"
// xpu.kubernetes.io/mem-unit-key: "mem-unit.llmops.transwarp.io/iluvatars"
const (
	AnnotationXPUResourceTypeKey = "xpu.kubernetes.io/resource-type-key"

	AnnotationXPUResourceCoresKey   = "xpu.kubernetes.io/cores-key"
	AnnotationXPUResourceKey        = "xpu.kubernetes.io/mem-key"
	AnnotationXPUResourceMemUnitKey = "xpu.kubernetes.io/mem-unit-key"
)

type NXPUClient struct {
	kclient *k8s.KClientset

	mu          sync.RWMutex
	avaliableAC map[string][]*models.ResourceQuotaItem
}

func NewXpuClient() *NXPUClient {
	kclient, err := k8s.NewKClientset()
	if err != nil {
		panic(err)
	}
	c := &NXPUClient{
		kclient:     kclient,
		mu:          sync.RWMutex{},
		avaliableAC: make(map[string][]*models.ResourceQuotaItem),
	}
	go c.initAvaliableAC(10 * time.Minute)
	return c
}

func (c *NXPUClient) initAvaliableAC(interval time.Duration) {
	c.refreshAvaliableAC()
	ticker := time.NewTicker(interval)
	defer ticker.Stop()

	for range ticker.C {
		c.refreshAvaliableAC()
	}
}

func (c *NXPUClient) refreshAvaliableAC() {
	avaliableAC, err := c.LimitACResources(context.Background())
	if err != nil {
		stdlog.Errorf("Failed to update AC resource cache: %v", err)
		return
	}

	// only keep type, quota set to zero
	for _, v := range avaliableAC {
		for _, item := range v {
			item.NominalQuota = util.ZeroWithUnit(item.NominalQuota)
			item.BorrowingLimit = ""
			item.LendingLimit = ""
		}
	}

	c.mu.Lock()
	defer c.mu.Unlock()
	c.avaliableAC = avaliableAC

	stdlog.Infof("AC resource cache updated successfully.")
}

func (c *NXPUClient) LimitACResources(ctx context.Context) (map[string][]*models.ResourceQuotaItem, error) {
	nxpus, err := xpu.Rg.ListXpus(ctx)
	if err != nil {
		return nil, err
	}

	return c.NXPUsToACResources(nxpus)
}
func (c *NXPUClient) NXPUGroupToResourceQuotaSpec(groupIDs []string) (*models.ResourceQuotaSpec, error) {
	spec := &models.ResourceQuotaSpec{
		// QuotaType:            models.QuotaTypeDynamic,
		AcceleratedComputing: make(map[string][]*models.ResourceQuotaItem),
	}

	groups, err := xpu.Rg.GroupLister.List(labels.Everything())
	if err != nil {
		return nil, err
	}

	nxpus := make([]*knm_v1.NodeXpu, 0)
	nodeNames := make(map[string]interface{}, 0)

	nxpuIDs := make([]string, 0)
	for _, group := range groups {
		if !util.Contains(groupIDs, group.GetName()) {
			continue
		}
		for _, node := range group.Spec.Nodes {
			xpuIDs := node.XpuIds
			// if group not include xpuIDs, only cpu and memory
			// if xpuIDs != nil && len(xpuIDs) > 0 {
			nodeNames[node.NodeName] = nil
			// }
			nxpuIDs = append(nxpuIDs, xpuIDs...)
		}
	}

	nxpuIDs = util.RemoveDuplicatesString(nxpuIDs)

	xpus, err := xpu.Rg.ListXpus(context.Background())
	if err != nil {
		return nil, err
	}

	for _, id := range nxpuIDs {
		for _, xpu := range xpus {
			if xpu.Spec.ID == id {
				nxpus = append(nxpus, xpu)
				break
			}
		}
	}

	ac, err := c.NXPUsToACResources(nxpus)
	if err != nil {
		return spec, nil
	}
	spec.AcceleratedComputing = ac

	// cpu and mem
	nns := make([]string, 0)
	for k, _ := range nodeNames {
		nns = append(nns, k)
	}
	totalCpu, totalMemory, err := c.kclient.CalNodesTotalCPUAndMemory(nns)
	if err != nil {
		return nil, err
	}
	spec.LimitsCpu = totalCpu
	spec.LimitsMemory = totalMemory
	spec.RequestsCpu = totalCpu
	spec.RequestsMemory = totalMemory
	spec.CPU = models.ResourceQuotaItem{
		Name:          corev1.ResourceCPU,
		NominalQuota:  totalCpu,
		QuotaItemType: models.QuotaItemTypeCPU,
		Unit:          "1",
	}
	spec.Memory = models.ResourceQuotaItem{
		Name:          corev1.ResourceMemory,
		NominalQuota:  totalMemory,
		QuotaItemType: models.QuotaItemTypeMem,
		Unit:          "1Gi",
	}

	models.ApplyDefaultQuotaForQueue(spec)

	return spec, nil
}

func (c *NXPUClient) NXPUGroupToACResources(groupIDs []string) (map[string][]*models.ResourceQuotaItem, error) {
	groups, err := xpu.Rg.GroupLister.List(labels.Everything())
	if err != nil {
		return nil, err
	}

	nxpus := make([]*knm_v1.NodeXpu, 0)

	nxpuIDs := make([]string, 0)
	for _, group := range groups {
		if !util.Contains(groupIDs, group.GetName()) {
			continue
		}
		for _, node := range group.Spec.Nodes {
			nxpuIDs = append(nxpuIDs, node.XpuIds...)
		}
	}

	nxpuIDs = util.RemoveDuplicatesString(nxpuIDs)

	xpus, err := xpu.Rg.ListXpus(context.Background())
	if err != nil {
		return nil, err
	}

	for _, id := range nxpuIDs {
		for _, xpu := range xpus {
			if xpu.Spec.ID == id {
				nxpus = append(nxpus, xpu)
				break
			}
		}
	}
	return c.NXPUsToACResources(nxpus)
}

type ACResource struct {
	Name             string
	CoresResourceKey string
	MemResourceKey   string
	Cores            resource.Quantity
	Mem              resource.Quantity
	MemUnit          resource.Quantity
}

func (c *NXPUClient) NXPUsToACResources(nxpus []*knm_v1.NodeXpu) (map[string][]*models.ResourceQuotaItem, error) {

	acMap := make(map[string]*ACResource)

	for _, nxpu := range nxpus {
		annotations := nxpu.GetAnnotations()
		resourceType := annotations[AnnotationXPUResourceTypeKey]
		coresKey := annotations[AnnotationXPUResourceCoresKey]
		memKey := annotations[AnnotationXPUResourceKey]
		memUnitKey := annotations[AnnotationXPUResourceMemUnitKey]

		if resourceType == "" || coresKey == "" || memKey == "" || memUnitKey == "" {
			continue
		}

		if _, ok := acMap[resourceType]; !ok {
			acMap[resourceType] = &ACResource{
				Name: resourceType,
			}
		}
		coresVal := annotations[coresKey]
		if coresVal == "" {
			continue
		}
		acMap[resourceType].CoresResourceKey = coresKey
		acMap[resourceType].Cores.Add(resource.MustParse(coresVal))

		memVal := annotations[memKey]
		memUnitVal := annotations[memUnitKey]
		if memVal == "" || memUnitVal == "" {
			continue
		}
		acMap[resourceType].MemResourceKey = memKey
		acMap[resourceType].MemUnit = resource.MustParse(memUnitVal)

		totalMem := resource.MustParse(memUnitVal)
		count := resource.MustParse(memVal)
		totalMem.Mul(count.Value())
		acMap[resourceType].Mem.Add(totalMem)
	}
	ac := make(map[string][]*models.ResourceQuotaItem)
	for _, v := range acMap {
		ac[v.Name] = []*models.ResourceQuotaItem{
			{
				Name:          corev1.ResourceName(v.CoresResourceKey),
				NominalQuota:  v.Cores.String(),
				Unit:          "1",
				QuotaItemType: models.QuotaItemTypeXPUCores,
			},
			{
				Name:          corev1.ResourceName(v.MemResourceKey),
				NominalQuota:  util.ToGiStrCeil(v.Mem),
				Unit:          v.MemUnit.String(),
				QuotaItemType: models.QuotaItemTypeXPUMem,
			},
		}
	}

	if err := c.applyDefaultACResourceQuota(&ac); err != nil {
		return nil, err
	}
	return ac, nil
}

func (c *NXPUClient) applyDefaultACResourceQuota(ac *map[string][]*models.ResourceQuotaItem) error {
	for k, v := range c.avaliableAC {
		if _, ok := (*ac)[k]; !ok {
			(*ac)[k] = v
		}
	}

	return nil
}

func (c *NXPUClient) RecalculateNamespaceResourceQuotaEffectByNXG(groupID string) (map[string]models.ResourceQuotaSpec, error) {
	bindings, err := xpu.Rg.BindLister.List(labels.Everything())
	if err != nil {
		return nil, err
	}
	// find effected namespace
	effectedNS := make(map[string][]string, 0)
	for _, binding := range bindings {
		if binding.Spec.NodeXpuGroupRef.Name == groupID {
			for _, subject := range binding.Spec.Subjects {
				effectedNS[subject.Namespace] = make([]string, 0)
			}
		}
	}
	// find namespace using nxg
	for _, binding := range bindings {
		for _, subject := range binding.Spec.Subjects {
			if _, ok := effectedNS[subject.Namespace]; ok {
				effectedNS[subject.Namespace] = append(effectedNS[subject.Namespace], binding.Spec.NodeXpuGroupRef.Name)
			}
		}
	}
	nsQuotaSpec := make(map[string]models.ResourceQuotaSpec, 0)
	for k, v := range effectedNS {
		groupIDs := util.RemoveDuplicatesString(v)
		spec, err := c.NXPUGroupToResourceQuotaSpec(groupIDs)
		if err != nil {
			stdlog.Errorf("Failed to convert nxg to resource quota: %v", err)
			return nil, err
		}
		nsQuotaSpec[k] = *spec
	}

	return nsQuotaSpec, err
}
