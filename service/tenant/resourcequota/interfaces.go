package resourcequota

import (
	"context"

	"transwarp.io/applied-ai/central-auth-service/models"
)

type QuotaService interface {
	GetQuota(ctx context.Context, namespace string) (*models.TenantResourceQuota, error)

	CreateOrUpdateQuota(ctx context.Context, namespace string, spec *models.ResourceQuotaSpec) error

	GetDefaultQuota() *models.TenantResourceQuota

	GetClusterLimits() (*models.ResourceQuotaSpec, error)

	ValidateQuota(spec *models.ResourceQuotaSpec) error

	CalculateQuotaFromXPUGroups(groupIDs []string) (*models.ResourceQuotaSpec, error)

	StartConfigWatcher(ctx context.Context) error

	StartXPUWatcher(ctx context.Context) error
}

type QuotaRepo interface {
	GetQuota(ctx context.Context, namespace string) (*models.ResourceQuotaSpec, error)

	SaveQuota(ctx context.Context, namespace string, spec *models.ResourceQuotaSpec) error

	DeleteQuota(ctx context.Context, namespace string) error
}
